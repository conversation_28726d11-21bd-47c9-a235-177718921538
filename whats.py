import pywhatkit


#pywhatkit
celular_cod = '+55'
celular = input(
    'Informe o Número do Celular \n '
    'Exemplo: 68984281313: '
    )

celular_formatado = celular_cod + celular
mensagem = input('Digite uma mensagem a ser enviada! ')

print('\n')
print('Whatsapp Send - Enviando Mensagem')
print('......................................................................................................................')
print('\n')
pywhatkit.sendwhatmsg_instantly(celular_formatado, mensagem, 15)
print('\n')
print(f'Mensagem Enviada......{mensagem}')
print(f'Número do celular.....{celular_formatado}')
print('.......................................................................................................................')

input('Pressione <enter> para Sair!')