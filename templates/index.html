<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador PIX QR Code - Parana Stillus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .qr-result {
            display: none;
            margin-top: 30px;
        }
        .loading {
            display: none;
        }
        .header-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .qr-code-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-qrcode header-icon"></i>
                            <h2 class="card-title mb-3">Gerador PIX QR Code</h2>
                            <p class="text-muted">Parana Stillus - Cavicchioli & Romera M. Construção</p>
                        </div>

                        <form id="pixForm">
                            <div class="mb-4">
                                <label for="banco" class="form-label">
                                    <i class="fas fa-university me-2"></i>Banco
                                </label>
                                <select class="form-control" id="banco" name="banco" required>
                                    <option value="1">PagSeguro (Padrão)</option>
                                    <option value="2">Inter-PJ</option>
                                    <option value="3">Nubank</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="valor" class="form-label">
                                    <i class="fas fa-dollar-sign me-2"></i>Valor (R$)
                                </label>
                                <input type="text" class="form-control" id="valor" name="valor" 
                                       placeholder="Ex: 1.00" required pattern="[0-9]+([.,][0-9]{1,2})?">
                                <div class="form-text">Use ponto ou vírgula para separar os centavos</div>
                            </div>

                            <div class="mb-4">
                                <label for="celular" class="form-label">
                                    <i class="fas fa-phone me-2"></i>Celular (WhatsApp)
                                </label>
                                <input type="tel" class="form-control" id="celular" name="celular" 
                                       placeholder="Ex: 68984281313" required pattern="[0-9]{10,11}">
                                <div class="form-text">Apenas números, sem DDD 55</div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic me-2"></i>Gerar PIX QR Code
                                </button>
                            </div>
                        </form>

                        <div class="loading text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Gerando...</span>
                            </div>
                            <p class="mt-2">Gerando QR Code PIX...</p>
                        </div>

                        <div id="result" class="qr-result">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>PIX Gerado com Sucesso!</h5>
                                <div id="resultInfo"></div>
                            </div>
                            
                            <div class="qr-code-container">
                                <img id="qrCodeImage" src="" alt="QR Code PIX" class="img-fluid mb-3" style="max-width: 300px;">
                                <div>
                                    <button class="btn btn-outline-primary me-2" onclick="copyPixCode()">
                                        <i class="fas fa-copy me-1"></i>Copiar Código PIX
                                    </button>
                                    <button class="btn btn-outline-success" onclick="downloadQR()">
                                        <i class="fas fa-download me-1"></i>Baixar QR Code
                                    </button>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Código PIX (Copie e Cole):</h6>
                                <textarea id="pixCode" class="form-control" rows="3" readonly></textarea>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fab fa-whatsapp me-2"></i>Envio via WhatsApp</h6>
                                <small>
                                    <strong>Importante:</strong> Para usar esta funcionalidade, certifique-se de que:
                                    <br>• O WhatsApp Web está aberto e logado no seu navegador
                                    <br>• O número do celular está correto e possui WhatsApp
                                </small>
                            </div>

                            <div class="d-grid gap-2 mt-3">
                                <button class="btn btn-success" onclick="enviarWhatsApp()">
                                    <i class="fab fa-whatsapp me-2"></i>Enviar via WhatsApp
                                </button>
                                <button class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-redo me-2"></i>Gerar Novo PIX
                                </button>
                            </div>
                        </div>

                        <div id="error" class="alert alert-danger" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorMessage"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPixData = null;

        document.getElementById('pixForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                banco: formData.get('banco'),
                valor: formData.get('valor'),
                celular: formData.get('celular')
            };

            // Mostrar loading
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('result').style.display = 'none';

            try {
                const response = await fetch('/gerar-pix', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    currentPixData = result;
                    showResult(result);
                } else {
                    showError(result.error || 'Erro desconhecido');
                }
            } catch (error) {
                showError('Erro de conexão: ' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
            }
        });

        function showResult(data) {
            document.getElementById('resultInfo').innerHTML = `
                <strong>Banco:</strong> ${data.banco}<br>
                <strong>Valor:</strong> R$ ${data.valor}<br>
                <strong>Celular:</strong> ${data.celular}<br>
                <strong>Data:</strong> ${data.data}
            `;
            
            document.getElementById('qrCodeImage').src = 'data:image/png;base64,' + data.qr_code_base64;
            document.getElementById('pixCode').value = data.chave_pix;
            document.getElementById('result').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        function copyPixCode() {
            const pixCode = document.getElementById('pixCode');
            pixCode.select();
            document.execCommand('copy');
            
            // Feedback visual
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Copiado!';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
            }, 2000);
        }

        function downloadQR() {
            if (currentPixData) {
                const link = document.createElement('a');
                link.href = 'data:image/png;base64,' + currentPixData.qr_code_base64;
                link.download = `qr_code_pix_${currentPixData.valor.replace('.', '_')}.png`;
                link.click();
            }
        }

        function resetForm() {
            document.getElementById('pixForm').reset();
            document.getElementById('result').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            currentPixData = null;
        }

        async function enviarWhatsApp() {
            if (!currentPixData) {
                showError('Nenhum PIX gerado para enviar');
                return;
            }

            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;

            // Mostrar loading no botão
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
            btn.disabled = true;

            try {
                const response = await fetch('/enviar-whatsapp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        celular: currentPixData.celular,
                        chave_pix: currentPixData.chave_pix,
                        valor: currentPixData.valor,
                        banco: currentPixData.banco
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Feedback de sucesso
                    btn.innerHTML = '<i class="fas fa-check me-2"></i>Enviado!';
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-success');

                    // Mostrar alerta de sucesso
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-2';
                    successAlert.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        ${result.message}
                        <br><small>Verifique seu WhatsApp Web para confirmar o envio.</small>
                    `;

                    document.getElementById('result').appendChild(successAlert);

                    // Remover alerta após 5 segundos
                    setTimeout(() => {
                        if (successAlert.parentNode) {
                            successAlert.remove();
                        }
                    }, 5000);

                } else {
                    showError(result.error || 'Erro ao enviar WhatsApp');

                    // Restaurar botão
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-outline-success');
                    btn.classList.add('btn-success');
                }
            } catch (error) {
                showError('Erro de conexão: ' + error.message);

                // Restaurar botão
                btn.innerHTML = originalText;
                btn.classList.remove('btn-outline-success');
                btn.classList.add('btn-success');
            } finally {
                btn.disabled = false;

                // Restaurar botão após 3 segundos se foi bem-sucedido
                if (btn.innerHTML.includes('Enviado!')) {
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.classList.remove('btn-outline-success');
                        btn.classList.add('btn-success');
                    }, 3000);
                }
            }
        }

        // Formatação automática do valor
        document.getElementById('valor').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d.,]/g, '');
            e.target.value = value;
        });

        // Formatação automática do celular
        document.getElementById('celular').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            e.target.value = value;
        });
    </script>
</body>
</html>
