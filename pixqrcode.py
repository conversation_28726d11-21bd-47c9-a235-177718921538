"""
#####################################################################

Exemplo com dados fictícios

#payload = Payload('Nome Sobrenome', '12345678900', '1.00', 'Cidade Ficticia', 'LOJA01')

#payload.gerarPayload()

Resultado


#00020126330014BR.GOV.BCB.PIX01111234567890052040000530398654041.005802BR5914Nome Sobrenome6015Cidade Ficticia62100506LOJA016304C8E4

Observação

#QR Code é gerado após a criação da Payload do Pix e salvo no diretório de desenvolvimento.

import pywhatkit



pywhatkit.sendwhatmsg_instantly("+55***********", "Pix Copie e Cole", 15)
"""


# Importando o módulo

import pywhatkit
from datetime import datetime
from pixqrcodegen import Payload
from time import sleep
from tkinter import *
from PIL import Image, ImageTk


diade = datetime.today().strftime("%d/ %m/ %Y")
emoji = '\U0001F4B5'


keys_pix = {
    'pagseguro': "+55***********",
    'inter': "5eed1690-8fe6-4977-b32f-4213089d3ac5",
    'nubank': "+*************",
}

while True:
    digita_banco = input(
        "Escolha o banco: . 1 - Pagseguro (Padrão) \n"
        "                   2 - Inter-PJ \n"
        "                   3 - Nubank \n"
        "                   ...:  "
    )

    # Checando banco
    if digita_banco == "2":
        CHAVEPIX = keys_pix['inter']
    elif digita_banco == "3":
        CHAVEPIX = keys_pix['nubank']
    else:
        CHAVEPIX = keys_pix['pagseguro']

    valorPagar = input(
        "Digite o Valor a Ser Pago \n "
        "no formato Exemplo: 1.00 \n "
        ".................:  "
    )

    # pywhatkit
    celular = input("Informe o Número do Celular \n " "Exemplo: ***********: ")
    celular_br = '+55' + celular
    # Parâmetros necessários
    payload = Payload("Parana Stillus", CHAVEPIX, valorPagar, "Acrelandia", "3280")

    # print(type(payload))

    # Chamando a função responsável para gerar a Payload Pix e o QR Code
    payload.gerarPayload()
    message = (
        "    Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
        f"valor de R$ {valorPagar}. Esse código é válido até {diade} \n"
        f"copie e cole o código abaixo {emoji} no pix *** Copie e Cole *** \n"
        "..............................................................................\n"
        "\n "
    )

    chavepixgerada = payload.obterValor()

    # Enviando imagem, para windows pip install pywin32
    imagePath = "pixqrcodegen.png"
    Caption = "Qr Code"

    print("\n")
    print("Pix Copie e Cole - Ir em Área pix e colar a chave no pix copie e cole")
    print(
        "......................................................................................................................"
    )
    print("\n")
    print(message + chavepixgerada)
    pywhatkit.sendwhatmsg_instantly(celular_br, message, 15)
    #sleep(5)
    pywhatkit.sendwhatmsg_instantly(celular_br, chavepixgerada, 17)
    pywhatkit.sendwhats_image(celular_br, imagePath, Caption, 19)

    print("\n")
    print(
        "......................................................................................................................."
    )
    print(f"Valor do Pix......{valorPagar}")
    print(f"Chave do Pix......{CHAVEPIX}")
    print(f"Número do celular..{celular_br}")

    class ImprimeQrCode:
        """Imprime Image QrCode que foi gerada"""

        def __init__(self, master=None):
            self.widget1 = Frame(master)
            self.widget1.pack()

            sleep(1)
            image = Image.open("pixqrcodegen.png")
            photo = ImageTk.PhotoImage(image)
            self.imagem = Label(master, text="adicionando", image=photo)
            self.imagem.image = photo
            self.imagem.pack()

            self.pixqr = Label(self.widget1, text="Pix QrCode")
            self.pixqr.pack()

    root = Tk()
    ImprimeQrCode(root)
    root.after(60 * 1000, root.destroy)  # 1000 ms = 1s
    root.mainloop()

    if int(input("Pressione <2> para Sair, ou Digite <1> para nova chave!!!..:")) == 2:
        break
