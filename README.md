# PIX QR Code Generator - Web Application

Aplicação web para geração de QR Codes PIX com interface moderna e containerizada com Docker.

## 🚀 Características

- **Interface Web Moderna**: Interface responsiva com Bootstrap 5
- **Geração de QR Code PIX**: Suporte completo ao padrão PIX brasileiro
- **Múl<PERSON>los Bancos**: PagSeguro, Inter-PJ e Nubank
- **Containerizada**: Aplicação totalmente dockerizada
- **Pronta para Produção**: Configuração com Nginx e Gunicorn

## 🏗️ Arquitetura

- **Backend**: Flask (Python)
- **Frontend**: HTML5 + Bootstrap 5 + JavaScript
- **Containerização**: Docker + Docker Compose
- **Servidor Web**: Gunicorn + Nginx (produção)
- **Geração QR Code**: qrcode + Pillow

## 📋 Pré-requisitos

- Docker
- Docker Compose

## 🚀 Instalação e Execução

### Desenvolvimento (apenas Flask)

```bash
# Clone o repositório
git clone <repository-url>
cd pixqrcode

# Execute com Docker Compose
docker-compose up --build
```

A aplicação estará disponível em: http://localhost:5000

### Produção (com Nginx)

```bash
# Execute com perfil de produção
docker-compose --profile production up --build -d
```

A aplicação estará disponível em: http://localhost

## 🐳 Comandos Docker

```bash
# Construir apenas a imagem
docker build -t pixqrcode .

# Executar container individual
docker run -p 5000:5000 pixqrcode

# Ver logs
docker-compose logs -f

# Parar serviços
docker-compose down

# Rebuild completo
docker-compose down && docker-compose up --build
```

## 📱 Como Usar

1. Acesse a aplicação web
2. Selecione o banco (PagSeguro, Inter-PJ ou Nubank)
3. Digite o valor do PIX
4. Informe o número do celular (WhatsApp)
5. Clique em "Gerar PIX QR Code"
6. Copie o código PIX ou baixe o QR Code

## 🔧 Configuração

### Chaves PIX

As chaves PIX estão configuradas no arquivo `app.py`:

```python
KEYS_PIX = {
    'pagseguro': "+*************",
    'inter': "5eed1690-8fe6-4977-b32f-4213089d3ac5",
    'nubank': "+*************",
}
```

### Variáveis de Ambiente

- `FLASK_ENV`: Ambiente da aplicação (development/production)
- `FLASK_APP`: Arquivo principal da aplicação

## 📁 Estrutura do Projeto

```
pixqrcode/
├── app.py                 # Aplicação Flask principal
├── requirements.txt       # Dependências Python
├── Dockerfile            # Configuração Docker
├── docker-compose.yml    # Orquestração Docker
├── nginx.conf           # Configuração Nginx
├── templates/
│   └── index.html       # Interface web
├── pixqrcodegen/
│   ├── __init__.py
│   └── pixqrcodegen.py  # Módulo geração PIX
└── README.md
```

## 🔒 Segurança

- Rate limiting configurado no Nginx
- Headers de segurança implementados
- Usuário não-root no container
- Validação de entrada nos formulários

## 🚀 Deploy em Produção

### Docker Swarm

```bash
docker stack deploy -c docker-compose.yml pixqrcode
```

### Kubernetes

```bash
# Gerar manifests Kubernetes
kompose convert

# Aplicar
kubectl apply -f .
```

## 🛠️ Desenvolvimento

### Executar localmente (sem Docker)

```bash
# Instalar dependências
pip install -r requirements.txt

# Executar aplicação
python app.py
```

### Estrutura de desenvolvimento

- `app.py`: Aplicação Flask principal
- `templates/`: Templates HTML
- `static/`: Arquivos estáticos (CSS, JS, imagens)
- `pixqrcodegen/`: Módulo de geração PIX

## 📊 Monitoramento

- Health check endpoint: `/health`
- Logs disponíveis via Docker: `docker-compose logs`

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 👥 Autores

- **Parana Stillus** - Cavicchioli & Romera M. Construção

---

## 📞 Suporte

Para suporte, entre em contato através dos canais oficiais da empresa.

## 🔗 Referências

- [Banco Central do Brasil - PIX](https://www.bcb.gov.br/estabilidadefinanceira/pix)
- [Manual de Padrões PIX](https://www.bcb.gov.br/content/estabilidadefinanceira/pix/Regulamento_Pix/II_ManualdePadroesparaIniciacaodoPix.pdf)

## 📝 Agradecimentos

[**@alvfig**](https://github.com/alvfig) - Obrigado pela alteração do código para string nativa do Python e pela alteração do código CRC16 adicionando zeros para esquerda quando necessário.
