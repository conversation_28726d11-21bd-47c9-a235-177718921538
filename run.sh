#!/bin/bash

# Script para executar a aplicação PIX QR Code

echo "🚀 Iniciando aplicação PIX QR Code..."

# Verificar se Docker está disponível
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "🐳 Docker detectado. Executando com Docker Compose..."
    
    # Verificar se o Docker daemon está rodando
    if docker info &> /dev/null; then
        echo "📦 Construindo e iniciando containers..."
        docker-compose up --build
    else
        echo "❌ Docker daemon não está rodando. Iniciando aplicação local..."
        python3 app.py
    fi
else
    echo "📱 Docker não encontrado. Executando aplicação local..."
    
    # Verificar se as dependências estão instaladas
    echo "🔍 Verificando dependências..."
    
    if ! python3 -c "import flask, crcmod, qrcode, PIL" &> /dev/null; then
        echo "📦 Instalando dependências..."
        pip install -r requirements.txt
    fi
    
    echo "🌐 Iniciando servidor Flask..."
    python3 app.py
fi
