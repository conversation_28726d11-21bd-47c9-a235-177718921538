#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demonstração da funcionalidade de envio via WhatsApp
Este script mostra como a funcionalidade funciona
"""

def demonstrar_envio_whatsapp():
    """Demonstra como funciona o envio via WhatsApp"""
    
    print("🚀 Demonstração: Envio PIX via WhatsApp")
    print("=" * 50)
    
    # Dados de exemplo
    dados_exemplo = {
        'celular': '+5568984281313',
        'valor': '10.00',
        'banco': 'PagSeguro',
        'chave_pix': '00020126330014BR.GOV.BCB.PIX01111234567890052040000530398654041.005802BR5914Nome Sobrenome6015Cidade Ficticia62100506LOJA016304C8E4'
    }
    
    print(f"📱 Celular: {dados_exemplo['celular']}")
    print(f"💰 Valor: R$ {dados_exemplo['valor']}")
    print(f"🏦 Banco: {dados_exemplo['banco']}")
    print(f"🔑 Chave PIX: {dados_exemplo['chave_pix'][:50]}...")
    
    print("\n📝 Mensagem que seria enviada:")
    print("-" * 30)
    
    from datetime import datetime
    data_hoje = datetime.today().strftime("%d/%m/%Y")
    emoji = '💵'
    
    message = (
        f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
        f"Valor de R$ {dados_exemplo['valor']}. Esse código é válido até {data_hoje}\n"
        f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
        f"{'.' * 78}\n"
    )
    
    print(message)
    print(f"Chave PIX: {dados_exemplo['chave_pix']}")
    
    print("\n🔧 Como funciona:")
    print("1. Usuário gera o PIX na interface web")
    print("2. Clica no botão 'Enviar via WhatsApp'")
    print("3. Sistema envia mensagem + chave PIX via PyWhatKit")
    print("4. PyWhatKit abre WhatsApp Web automaticamente")
    print("5. Mensagens são enviadas para o número informado")
    
    print("\n⚠️  Requisitos:")
    print("• WhatsApp Web deve estar aberto e logado")
    print("• Ambiente com interface gráfica (não funciona em servidor)")
    print("• PyWhatKit instalado: pip install pywhatkit")
    
    print("\n✅ Funcionalidade implementada com sucesso!")
    print("A aplicação web agora possui o botão 'Enviar via WhatsApp'")

if __name__ == "__main__":
    demonstrar_envio_whatsapp()
