#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, jsonify, send_file
from pixqrcodegen import Payload
import os
import base64
from io import BytesIO
from datetime import datetime
import tempfile
from time import sleep
from whatsapp_fallback import gerar_link_whatsapp, enviar_whatsapp_fallback

app = Flask(__name__)

# Configuração das chaves PIX
KEYS_PIX = {
    'pagseguro': "+*************",
    'inter': "5eed1690-8fe6-4977-b32f-4213089d3ac5",
    'nubank': "+*************",
}

@app.route('/')
def index():
    """Página principal com formulário para gerar QR Code PIX"""
    return render_template('index.html')

@app.route('/gerar-pix', methods=['POST'])
def gerar_pix():
    """Endpoint para gerar QR Code PIX"""
    try:
        # Receber dados do formulário
        data = request.get_json()
        
        banco = data.get('banco', '1')
        valor = data.get('valor', '').replace(',', '.')
        celular = data.get('celular', '')
        
        # Validações básicas
        if not valor or not celular:
            return jsonify({'error': 'Valor e celular são obrigatórios'}), 400
        
        try:
            float(valor)
        except ValueError:
            return jsonify({'error': 'Valor deve ser um número válido'}), 400
        
        # Selecionar chave PIX baseada no banco
        if banco == "2":
            chave_pix = KEYS_PIX['inter']
            banco_nome = "Inter-PJ"
        elif banco == "3":
            chave_pix = KEYS_PIX['nubank']
            banco_nome = "Nubank"
        else:
            chave_pix = KEYS_PIX['pagseguro']
            banco_nome = "PagSeguro"
        
        # Criar diretório temporário para o QR Code
        temp_dir = tempfile.mkdtemp()
        
        # Gerar payload PIX
        payload = Payload(
            nome="Parana Stillus",
            chavepix=chave_pix,
            valor=valor,
            cidade="Acrelandia",
            txtId="3280",
            diretorio=temp_dir
        )
        
        payload.gerarPayload()
        chave_pix_gerada = payload.obterValor()
        
        # Ler o QR Code gerado
        qr_path = os.path.join(temp_dir, 'pixqrcodegen.png')
        
        # Converter imagem para base64 para enviar via JSON
        with open(qr_path, 'rb') as img_file:
            img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Data atual
        data_hoje = datetime.today().strftime("%d/%m/%Y")
        
        # Preparar mensagem
        emoji = '💵'
        celular_br = f'+55{celular}'
        
        message = (
            f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
            f"Valor de R$ {valor}. Esse código é válido até {data_hoje}\n"
            f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
            f"{'.' * 78}\n"
        )
        
        # Limpar arquivo temporário
        os.remove(qr_path)
        os.rmdir(temp_dir)
        
        return jsonify({
            'success': True,
            'banco': banco_nome,
            'valor': valor,
            'celular': celular_br,
            'chave_pix': chave_pix_gerada,
            'qr_code_base64': img_base64,
            'message': message,
            'data': data_hoje
        })
        
    except Exception as e:
        return jsonify({'error': f'Erro interno: {str(e)}'}), 500

@app.route('/enviar-whatsapp', methods=['POST'])
def enviar_whatsapp():
    """Endpoint para enviar PIX via WhatsApp"""
    try:
        data = request.get_json()

        celular = data.get('celular', '')
        chave_pix = data.get('chave_pix', '')
        valor = data.get('valor', '')
        banco = data.get('banco', '')

        if not celular or not chave_pix:
            return jsonify({'error': 'Celular e chave PIX são obrigatórios'}), 400

        # Preparar mensagem
        data_hoje = datetime.today().strftime("%d/%m/%Y")
        emoji = '💵'

        message = (
            f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
            f"Valor de R$ {valor}. Esse código é válido até {data_hoje}\n"
            f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
            f"{'.' * 78}\n"
        )

        # Enviar mensagem via WhatsApp
        try:
            # Detectar se está rodando em container Docker
            is_docker = os.path.exists('/.dockerenv')

            # Se estiver em Docker, usar diretamente o fallback
            if is_docker:
                raise Exception("Ambiente Docker detectado - usando fallback direto")

            # Importar pywhatkit apenas quando necessário (ambiente desktop)
            try:
                # Configurar ambiente X11 se necessário
                if not os.environ.get('DISPLAY'):
                    os.environ['DISPLAY'] = ':99'

                # Criar arquivo Xauthority se não existir
                xauth_file = os.path.expanduser('~/.Xauthority')
                if not os.path.exists(xauth_file):
                    try:
                        open(xauth_file, 'a').close()
                        os.chmod(xauth_file, 0o600)
                    except:
                        pass  # Ignorar se não conseguir criar

                import pywhatkit

            except ImportError:
                return jsonify({
                    'error': 'PyWhatKit não está instalado',
                    'details': 'Execute: pip install pywhatkit'
                }), 500
            except Exception as import_error:
                error_msg = str(import_error)
                details = 'Verifique se você está executando em um ambiente com interface gráfica'

                # Mensagens de erro mais específicas
                if 'Xauthority' in error_msg:
                    details = 'Problema com autorização X11. Tente executar em ambiente desktop ou configure DISPLAY corretamente.'
                elif 'DISPLAY' in error_msg:
                    details = 'Variável DISPLAY não configurada. Execute em ambiente desktop ou configure display virtual.'
                elif 'connect to display' in error_msg.lower():
                    details = 'Não foi possível conectar ao display. Certifique-se de que está em ambiente desktop.'

                return jsonify({
                    'error': f'Erro ao carregar PyWhatKit: {error_msg}',
                    'details': details,
                    'solution': 'Para usar esta funcionalidade, execute a aplicação em um ambiente desktop com interface gráfica.'
                }), 500

            # Enviar mensagem de texto
            pywhatkit.sendwhatmsg_instantly(celular, message, 15)
            sleep(5)

            # Enviar chave PIX
            pywhatkit.sendwhatmsg_instantly(celular, chave_pix, 17)

            # Se houver QR Code, enviar imagem (opcional)
            # pywhatkit.sendwhats_image(celular, "pixqrcodegen.png", "QR Code PIX", 19)

            return jsonify({
                'success': True,
                'message': 'PIX enviado via WhatsApp com sucesso!'
            })

        except Exception as whats_error:
            # Se PyWhatKit falhar, usar método alternativo
            try:
                # Gerar link direto do WhatsApp
                link_resultado = gerar_link_whatsapp(celular, chave_pix, valor)

                if link_resultado['success']:
                    return jsonify({
                        'success': True,
                        'message': 'Link do WhatsApp gerado com sucesso!',
                        'whatsapp_link': link_resultado['whatsapp_link'],
                        'fallback': True,
                        'details': 'Clique no link para abrir o WhatsApp e enviar a mensagem'
                    })
                else:
                    raise Exception(link_resultado['error'])

            except Exception as fallback_error:
                return jsonify({
                    'error': f'Erro ao enviar WhatsApp: {str(whats_error)}',
                    'fallback_error': f'Erro no método alternativo: {str(fallback_error)}',
                    'details': 'Verifique se você está executando em um ambiente com interface gráfica ou use o link direto do WhatsApp'
                }), 500

    except Exception as e:
        return jsonify({'error': f'Erro interno: {str(e)}'}), 500

@app.route('/download-qr/<path:filename>')
def download_qr(filename):
    """Endpoint para download do QR Code"""
    try:
        return send_file(filename, as_attachment=True, download_name='qr_code_pix.png')
    except Exception as e:
        return jsonify({'error': f'Arquivo não encontrado: {str(e)}'}), 404

@app.route('/health')
def health():
    """Endpoint de health check"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    # Criar diretório para templates se não existir
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Executar aplicação
    app.run(host='0.0.0.0', port=5000, debug=True)
