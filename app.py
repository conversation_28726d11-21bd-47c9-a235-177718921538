#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, jsonify, send_file
from pixqrcodegen import Payload
import os
import base64
from io import BytesIO
from datetime import datetime
import tempfile

app = Flask(__name__)

# Configuração das chaves PIX
KEYS_PIX = {
    'pagseguro': "+*************",
    'inter': "5eed1690-8fe6-4977-b32f-4213089d3ac5",
    'nubank': "+*************",
}

@app.route('/')
def index():
    """Página principal com formulário para gerar QR Code PIX"""
    return render_template('index.html')

@app.route('/gerar-pix', methods=['POST'])
def gerar_pix():
    """Endpoint para gerar QR Code PIX"""
    try:
        # Receber dados do formulário
        data = request.get_json()
        
        banco = data.get('banco', '1')
        valor = data.get('valor', '').replace(',', '.')
        celular = data.get('celular', '')
        
        # Validações básicas
        if not valor or not celular:
            return jsonify({'error': 'Valor e celular são obrigatórios'}), 400
        
        try:
            float(valor)
        except ValueError:
            return jsonify({'error': 'Valor deve ser um número válido'}), 400
        
        # Selecionar chave PIX baseada no banco
        if banco == "2":
            chave_pix = KEYS_PIX['inter']
            banco_nome = "Inter-PJ"
        elif banco == "3":
            chave_pix = KEYS_PIX['nubank']
            banco_nome = "Nubank"
        else:
            chave_pix = KEYS_PIX['pagseguro']
            banco_nome = "PagSeguro"
        
        # Criar diretório temporário para o QR Code
        temp_dir = tempfile.mkdtemp()
        
        # Gerar payload PIX
        payload = Payload(
            nome="Parana Stillus",
            chavepix=chave_pix,
            valor=valor,
            cidade="Acrelandia",
            txtId="3280",
            diretorio=temp_dir
        )
        
        payload.gerarPayload()
        chave_pix_gerada = payload.obterValor()
        
        # Ler o QR Code gerado
        qr_path = os.path.join(temp_dir, 'pixqrcodegen.png')
        
        # Converter imagem para base64 para enviar via JSON
        with open(qr_path, 'rb') as img_file:
            img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Data atual
        data_hoje = datetime.today().strftime("%d/%m/%Y")
        
        # Preparar mensagem
        emoji = '💵'
        celular_br = f'+55{celular}'
        
        message = (
            f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
            f"Valor de R$ {valor}. Esse código é válido até {data_hoje}\n"
            f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
            f"{'.' * 78}\n"
        )
        
        # Limpar arquivo temporário
        os.remove(qr_path)
        os.rmdir(temp_dir)
        
        return jsonify({
            'success': True,
            'banco': banco_nome,
            'valor': valor,
            'celular': celular_br,
            'chave_pix': chave_pix_gerada,
            'qr_code_base64': img_base64,
            'message': message,
            'data': data_hoje
        })
        
    except Exception as e:
        return jsonify({'error': f'Erro interno: {str(e)}'}), 500

@app.route('/download-qr/<path:filename>')
def download_qr(filename):
    """Endpoint para download do QR Code"""
    try:
        return send_file(filename, as_attachment=True, download_name='qr_code_pix.png')
    except Exception as e:
        return jsonify({'error': f'Arquivo não encontrado: {str(e)}'}), 404

@app.route('/health')
def health():
    """Endpoint de health check"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    # Criar diretório para templates se não existir
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Executar aplicação
    app.run(host='0.0.0.0', port=5000, debug=True)
