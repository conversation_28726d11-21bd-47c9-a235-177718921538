#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da aplicação com fallback funcionando
"""

from flask import Flask, render_template, request, jsonify
import json
from whatsapp_fallback import gerar_link_whatsapp

app = Flask(__name__)

@app.route('/')
def index():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Teste PIX WhatsApp</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 600px; margin: 0 auto; }
            .form-group { margin: 20px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #25D366; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            button:hover { background: #128C7E; }
            .result { margin-top: 30px; padding: 20px; background: #f0f0f0; border-radius: 5px; }
            .link { color: #25D366; text-decoration: none; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Teste PIX WhatsApp (Fallback)</h1>
            
            <form id="pixForm">
                <div class="form-group">
                    <label>Banco:</label>
                    <select id="banco">
                        <option value="PagSeguro">PagSeguro</option>
                        <option value="Inter-PJ">Inter-PJ</option>
                        <option value="Nubank">Nubank</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Valor (R$):</label>
                    <input type="text" id="valor" placeholder="10.00" value="25.50">
                </div>
                
                <div class="form-group">
                    <label>Celular:</label>
                    <input type="text" id="celular" placeholder="***********" value="***********">
                </div>
                
                <button type="submit">📱 Gerar Link WhatsApp</button>
            </form>
            
            <div id="result" class="result" style="display: none;">
                <h3>✅ Link Gerado com Sucesso!</h3>
                <p id="details"></p>
                <p><a id="whatsappLink" class="link" target="_blank">🔗 Clique aqui para abrir WhatsApp</a></p>
            </div>
        </div>
        
        <script>
            document.getElementById('pixForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const data = {
                    banco: document.getElementById('banco').value,
                    valor: document.getElementById('valor').value,
                    celular: '+55' + document.getElementById('celular').value,
                    chave_pix: '00020126330014BR.GOV.BCB.PIX01111234567890052040000530398654041.005802BR5914Nome Sobrenome6015Cidade Ficticia62100506LOJA016304C8E4'
                };
                
                try {
                    const response = await fetch('/test-whatsapp', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        document.getElementById('details').innerHTML = 
                            `<strong>Banco:</strong> ${data.banco}<br>
                             <strong>Valor:</strong> R$ ${data.valor}<br>
                             <strong>Celular:</strong> ${data.celular}`;
                        
                        document.getElementById('whatsappLink').href = result.whatsapp_link;
                        document.getElementById('result').style.display = 'block';
                        
                        // Abrir automaticamente
                        window.open(result.whatsapp_link, '_blank');
                    } else {
                        alert('Erro: ' + result.error);
                    }
                } catch (error) {
                    alert('Erro de conexão: ' + error.message);
                }
            });
        </script>
    </body>
    </html>
    """

@app.route('/test-whatsapp', methods=['POST'])
def test_whatsapp():
    """Endpoint de teste para WhatsApp fallback"""
    try:
        data = request.get_json()
        
        celular = data.get('celular', '')
        chave_pix = data.get('chave_pix', '')
        valor = data.get('valor', '')
        banco = data.get('banco', '')
        
        print(f"📱 Testando envio para: {celular}")
        print(f"💰 Valor: R$ {valor}")
        print(f"🏦 Banco: {banco}")
        
        # Usar diretamente o fallback
        resultado = gerar_link_whatsapp(celular, chave_pix, valor)
        
        if resultado['success']:
            return jsonify({
                'success': True,
                'message': 'Link do WhatsApp gerado com sucesso!',
                'whatsapp_link': resultado['whatsapp_link'],
                'fallback': True,
                'details': 'Clique no link para abrir o WhatsApp'
            })
        else:
            return jsonify({
                'success': False,
                'error': resultado['error']
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro interno: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🧪 Iniciando aplicação de teste...")
    print("📱 Acesse: http://localhost:5000")
    print("🔗 Esta versão usa apenas o fallback (link direto)")
    app.run(host='0.0.0.0', port=5000, debug=True)
