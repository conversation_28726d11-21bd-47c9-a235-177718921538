Installation and Supported Versions
PyWhatKit is available on PyPi:

python3 -m pip install pywhatkit
pip3 install pywhatkit
PyWhatKit officially supports Python 3.8+.

Cloning the Repository
git clone https://github.com/Ankit404butfound/PyWhatKit.git


# <PERSON><PERSON><PERSON><PERSON><PERSON>

https://wiki.archlinux.org/title/Xhost

The 'cannot connect to X server :0.0' output
Warning: This command disables access control, meaning that any user on the system, or on your network if <PERSON> is listening on the network, has access to your $DISPLAY without any authentication. This opens a security hole on your system that allows other users to launch applications (including key loggers) on your X server.
The above command xhost + will get you rid of that output, albeit momentarily; one way of getting permanently rid of this issue, among many, is to add

    xhost + > /dev/null

to your ~/.bashrc file. 
This way, each time you fire up the terminal, the command gets executed. If you do not yet have a .bashrc file in your home directory, it is OK to create one with just this line in it