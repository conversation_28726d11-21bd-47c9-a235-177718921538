#!/bin/bash

# Script de inicialização para Docker com suporte ao PyWhatKit

echo "🚀 Iniciando aplicação PIX QR Code com suporte WhatsApp..."

# Iniciar display virtual para PyWhatKit (apenas em container)
if [ -f /.dockerenv ]; then
    echo "📺 Iniciando display virtual (Xvfb)..."
    Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
    export DISPLAY=:99
    sleep 2
fi

# Iniciar aplicação
echo "🌐 Iniciando servidor Flask..."
exec gunicorn --bind 0.0.0.0:5000 --workers 2 --timeout 120 app:app
