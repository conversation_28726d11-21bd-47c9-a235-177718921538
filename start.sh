#!/bin/bash

# Script de inicialização para Docker com suporte ao PyWhatKit

echo "🚀 Iniciando aplicação PIX QR Code com suporte WhatsApp..."

# Configurar ambiente X11 para PyWhatKit
if [ -f /.dockerenv ]; then
    echo "📺 Configurando ambiente gráfico virtual..."

    # Criar arquivo Xauthority se não existir
    touch ~/.Xauthority

    # Iniciar display virtual
    Xvfb :99 -screen 0 1024x768x24 -ac +extension GLX +render -noreset > /dev/null 2>&1 &
    export DISPLAY=:99

    # Aguardar Xvfb inicializar
    sleep 3

    # Verificar se display está funcionando
    if xdpyinfo -display :99 > /dev/null 2>&1; then
        echo "✅ Display virtual configurado com sucesso"
    else
        echo "⚠️  Aviso: Display virtual pode não estar funcionando corretamente"
    fi
else
    echo "🖥️  Executando em ambiente desktop"
fi

# Iniciar aplicação
echo "🌐 Iniciando servidor Flask..."
exec gunicorn --bind 0.0.0.0:5000 --workers 2 --timeout 120 app:app
