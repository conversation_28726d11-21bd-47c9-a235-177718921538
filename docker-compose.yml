version: '3.8'

services:
  pixqrcode-web:
    build: .
    container_name: pixqrcode-app
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_APP=app.py
    volumes:
      # Volume para logs (opcional)
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pixqrcode-network

  # Nginx reverse proxy (opcional, para produção)
  nginx:
    image: nginx:alpine
    container_name: pixqrcode-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # Para certificados SSL (opcional)
    depends_on:
      - pixqrcode-web
    restart: unless-stopped
    networks:
      - pixqrcode-network
    profiles:
      - production  # Só ativa com --profile production

networks:
  pixqrcode-network:
    driver: bridge

volumes:
  logs:
    driver: local
