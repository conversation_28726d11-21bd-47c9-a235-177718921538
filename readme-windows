Source:
https://pt.stackoverflow.com/questions/497925/como-ativar-o-virtualenv-no-prompt-de-comando-do-windows
https://www.appsloveworld.com/coding/python3x/53/create-a-windows-10-desktop-shortcut-to-run-a-python-script-in-a-venv-virtual-env
https://stackoverflow.com/questions/69592796/create-a-windows-10-desktop-shortcut-to-run-a-python-script-in-a-venv-virtual-en


# Venv
Exemplo:

python -m venv F:\Stillus\Desktop\PixQrCode venv

D:\projeto> .\venv\Scripts\activate.ps1
(venv) D:\projeto>


# Script run venv

(1) Create the following myscript.bat file and save it in the same directory as PyProject:

@echo off
cmd /k "cd /d C:\Users\<USER>\Documents\PyProject\.venv\Scripts & .\activate & cd /d C:\Users\<USER>\Documents\PyProject & py -3 .\myscript.py"

(2) Right-click on that myscript.bat file in the PyProject directory and "Create Shortcut". Drag that shortcut icon to the Desktop, and right-click to view "Properties." They should read:

Target: C:\Users\<USER>\Documents\PyProject\myscript.bat

Start In: C:\Users\<USER>\Documents\PyProject

Shortcut Key: None

Start In: Normal Window

The cmd window that is created after double-clicking on the icon remains open for the duration of the program.

