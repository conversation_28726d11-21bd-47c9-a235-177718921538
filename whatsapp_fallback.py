#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Módulo alternativo para envio via WhatsApp
Funciona sem interface gráfica usando WhatsApp Business API ou outras alternativas
"""

import requests
import json
from datetime import datetime

def enviar_whatsapp_fallback(celular, chave_pix, valor, banco):
    """
    Função alternativa para envio via WhatsApp
    Esta é uma implementação de fallback que pode ser adaptada para diferentes APIs
    """
    
    try:
        # Preparar mensagem
        data_hoje = datetime.today().strftime("%d/%m/%Y")
        emoji = '💵'
        
        message = (
            f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
            f"Valor de R$ {valor}. Esse código é válido até {data_hoje}\n"
            f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
            f"{'.' * 78}\n\n"
            f"{chave_pix}"
        )
        
        # Simular envio (aqui você pode integrar com APIs reais)
        print(f"📱 Simulando envio para: {celular}")
        print(f"💰 Valor: R$ {valor}")
        print(f"🏦 Banco: {banco}")
        print(f"📝 Mensagem:")
        print("-" * 50)
        print(message)
        print("-" * 50)
        
        # Aqui você pode integrar com:
        # 1. WhatsApp Business API
        # 2. Twilio WhatsApp API
        # 3. Outras APIs de mensageria
        
        # Exemplo de integração com API (descomentado quando tiver API real):
        """
        api_url = "https://api.whatsapp.com/send"
        payload = {
            "phone": celular,
            "text": message
        }
        response = requests.post(api_url, json=payload)
        """
        
        return {
            'success': True,
            'message': 'PIX preparado para envio via WhatsApp (modo simulação)',
            'details': f'Mensagem preparada para {celular}'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'Erro no fallback: {str(e)}'
        }

def gerar_link_whatsapp(celular, chave_pix, valor):
    """
    Gera link direto do WhatsApp que pode ser aberto pelo usuário
    Esta é uma alternativa que sempre funciona
    """
    
    try:
        # Limpar número do celular
        numero_limpo = celular.replace('+', '').replace('-', '').replace(' ', '')
        if not numero_limpo.startswith('55'):
            numero_limpo = '55' + numero_limpo
        
        # Preparar mensagem
        data_hoje = datetime.today().strftime("%d/%m/%Y")
        emoji = '💵'
        
        message = (
            f"Olá, Chave Pix enviada por Parana Stillus - Cavicchioli & Romera M. Construção\n"
            f"Valor de R$ {valor}. Esse código é válido até {data_hoje}\n"
            f"Copie e cole o código abaixo {emoji} no pix *** Copie e Cole ***\n"
            f"{'.' * 78}\n\n"
            f"{chave_pix}"
        )
        
        # Codificar mensagem para URL
        import urllib.parse
        message_encoded = urllib.parse.quote(message)
        
        # Gerar link do WhatsApp
        whatsapp_link = f"https://wa.me/{numero_limpo}?text={message_encoded}"
        
        return {
            'success': True,
            'whatsapp_link': whatsapp_link,
            'message': 'Link do WhatsApp gerado com sucesso'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'Erro ao gerar link: {str(e)}'
        }

if __name__ == "__main__":
    # Teste da funcionalidade
    resultado = enviar_whatsapp_fallback(
        "+5568984281313",
        "00020126330014BR.GOV.BCB.PIX01111234567890052040000530398654041.005802BR5914Nome Sobrenome6015Cidade Ficticia62100506LOJA016304C8E4",
        "10.00",
        "PagSeguro"
    )
    print(json.dumps(resultado, indent=2, ensure_ascii=False))
    
    # Teste do link
    link_resultado = gerar_link_whatsapp(
        "+5568984281313",
        "00020126330014BR.GOV.BCB.PIX01111234567890052040000530398654041.005802BR5914Nome Sobrenome6015Cidade Ficticia62100506LOJA016304C8E4",
        "10.00"
    )
    print("\nLink gerado:")
    print(link_resultado['whatsapp_link'] if link_resultado['success'] else link_resultado['error'])
